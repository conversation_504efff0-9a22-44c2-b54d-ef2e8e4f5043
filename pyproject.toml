[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "paissive_income"
version = "0.1.0"
description = "pAIssive Income project - AI-powered passive income generation platform"
readme = "README.md"
requires-python = ">=3.10"
license = {file = "LICENSE"}
authors = [
    {name = "pAIssive Income Team"}
]
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "fastapi>=0.95.0",
    "uvicorn>=0.22.0",
    "pydantic>=2.0.0",
    "redis>=4.5.0",
    "flask>=2.0.1",
    "httpx>=0.24.0",
    # OpenTelemetry dependencies (pinned to avoid conflicts)
    "opentelemetry-api>=1.30.0,<1.32.0",
    "opentelemetry-sdk>=1.30.0,<1.32.0",
    "opentelemetry-exporter-otlp-proto-grpc>=1.30.0,<1.32.0",
    "opentelemetry-exporter-otlp-proto-http>=1.30.0,<1.32.0",
    "opentelemetry-semantic-conventions>=0.51b0,<0.54b0",
    "wrapt>=1.14.0,<1.16.0",
    "backoff>=1.10.0,<2.0.0",
    # Add other dependencies as needed
    "bandit[toml]",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "pytest-asyncio>=0.21.0",
    "pytest-xdist>=3.5.0",
]
agents = [
    "crewai>=0.120.0",  # AI agent orchestration framework
]
memory = [
    "mem0ai>=0.1.100",  # Memory layer for AI agents
    "qdrant-client>=1.9.1",  # Vector database client for mem0
    "openai>=1.33.0",  # Required for mem0 embeddings
]
ml = [
    "torch>=1.10.0",
    "transformers>=4.20.0",
    "sentence-transformers>=2.2.0",
    "numpy>=1.24.3",
    "scikit-learn>=1.0.0",
]

[project.urls]
homepage = "https://github.com/yourusername/pAIssive_income"
bug_tracker = "https://github.com/yourusername/pAIssive_income/issues"

[tool.setuptools]
packages = ["services"]
package-dir = {"" = "."}
package-data = {"services" = ["**/*.json"]}

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
asyncio_default_fixture_loop_scope = "function"
addopts = [
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-fail-under=15",
    "--verbose",
    "--tb=short"
]

[tool.coverage.run]
source = ["."]
parallel = true
omit = [
    "tests/*",
    "*/tests/*",
    ".venv/*",
    "venv/*",
    "env/*",
    "setup.py",
    "fix_*.py",
    "setup_*.py",
    "*/examples/*",
    "*/cli/*",
    "*/site-packages/*",
    "test_*.py",
    "verify_*.py",
    "marketing/*",
    "monetization/*",
    "niche_analysis/*",
    "collaboration/*",
    "ai_models/benchmarking/*",
    "ai_models/caching/*",
    "ai_models/fine_tuning/*",
    "ai_models/metrics/*",
    "ai_models/optimization/*",
    "ai_models/serving/*",
    "common_utils/secrets/*",
    "common_utils/logging/alert_system.py",
    "common_utils/logging/centralized_logging.py",
    "common_utils/logging/dashboard_auth.py",
    "common_utils/logging/ml_log_analysis.py",
    "dev_tools/*",
    "scripts/*",
    "migrations/*",
    "artist_experiments/*"
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "raise ImportError",
    "if __name__ == .__main__.:",
    "if self.debug:",
    "@abstractmethod",
    "pass",
    "if TYPE_CHECKING:",
    "except ImportError:"
]
fail_under = 15

[tool.ruff]
target-version = "py310"
line-length = 100
select = ["E", "F", "B", "S", "I", "N", "UP", "ANN", "C4", "SIM", "ARG", "PTH", "RUF", "ERA", "TRY", "PLR", "PLW", "PLE", "BLE", "ISC"]
ignore = []

[tool.ruff.per-file-ignores]
"scripts/fix/fix_security_issues.py" = ["S603"]
"scripts/run/run_github_actions_locally.py" = ["S603", "G004", "TRY300", "RUF013", "RUF005"]
"scripts/setup/regenerate_venv.py" = ["S603", "TRY300"]
"scripts/setup/setup_pre_commit.py" = ["S603"]
"scripts/setup/setup_dev_environment.py" = ["S603"]

[tool.mypy]
python_version = "3.10"
exclude = ["ui/react_frontend", "scripts/__init__.py", ".github/scripts/__init__.py", ".github/scripts"]
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = false
ignore_missing_imports = true
warn_return_any = false
warn_unused_configs = true
warn_redundant_casts = true
warn_unused_ignores = false
warn_no_return = false
warn_unreachable = false
show_error_codes = true
cache_dir = ".mypy_cache"
incremental = true
follow_imports = "skip"
follow_imports_for_stubs = true
namespace_packages = true
explicit_package_bases = true
mypy_path = "mypy_stubs"
files = ["!scripts/__init__.py", "!.github/scripts/__init__.py"]

[[tool.mypy.overrides]]
module = "mypy.plugins.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "run_ui"
ignore_errors = true

[[tool.mypy.overrides]]
module = "migrations.env"
ignore_errors = true

[[tool.mypy.overrides]]
module = "users.password_reset"
ignore_errors = true

[[tool.mypy.overrides]]
module = "api.utils.auth"
ignore_errors = true

[[tool.mypy.overrides]]
module = "test_sarif_utils"
ignore_errors = true

[[tool.mypy.overrides]]
module = "run_mcp_tests"
ignore_errors = true

[[tool.mypy.overrides]]
module = "install_mcp_sdk"
ignore_errors = true

[[tool.mypy.overrides]]
module = "scripts.manage_quality"
ignore_errors = true

[[tool.mypy.overrides]]
module = "tests.api.*"
ignore_errors = true
